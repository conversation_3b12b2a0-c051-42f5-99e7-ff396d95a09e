package com.jlr.ecp.notification.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.jaguar.api.wechat.ShortLinkByWechatApi;
import com.jlr.ecp.jaguar.api.wechat.dto.GenUrlLinkReqDto;
import com.jlr.ecp.notification.enums.ErrorCodeConstants;
import com.jlr.ecp.notification.service.ShortLinkService;
import com.jlr.ecp.notification.util.sign.AbstractSignature;
import com.jlr.ecp.notification.util.sign.SignatureUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

import static com.jlr.ecp.framework.common.pojo.CommonResult.error;
import static com.jlr.ecp.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ShortLinkServiceImpl implements ShortLinkService {

    @Value("${jaguarlandrover.shortLinkUrl}")
    String jlrShortLinkServiceUrl;

    @Value("${jaguarlandrover.appKey}")
    String appKey;

    @Value("${jaguarlandrover.secret}")
    String secret;

    @Resource
    RestTemplate restTemplate;

    @Resource
    ShortLinkByWechatApi shorLinkByWechatApi;

    @Override
    public CommonResult<String> genShortLink(String path) {
        log.info("短链生成的方法，path：{}", path);
        if (StrUtil.isBlank(path)) {
            return success("");
        }
        // 发起调用获取短链
        //年后会改为 POST 请求，并加签校验
        Map<String, String> postParameters = new HashMap<>();
        postParameters.put("path", path);
        ResponseEntity<JSONObject> response;
        try {
            HttpHeaders headers = SignatureUtil.headers(postParameters, "/wxma/wx/urlLink", HttpMethod.POST.name(), appKey, secret);
            URI uri = UriComponentsBuilder.fromUriString(jlrShortLinkServiceUrl).build().toUri();
            RequestEntity<Map<String, String>> requestEntity = RequestEntity.post(uri).headers(headers).body(postParameters);
            log.info("请求的完整path：{}",path);
            response = restTemplate.exchange(requestEntity, JSONObject.class);
        } catch (Exception e) {
            log.info("调用短链生成服务出错:{}", e.getMessage());
            return error(ErrorCodeConstants.SMS_SHORT_LINK_FETCH_FAIL);
        }
        if (HttpStatus.OK.equals(response.getStatusCode())) {
            JSONObject body = response.getBody();
            Integer code = body.getInteger("code");
            if (code.equals(AbstractSignature.HTTP_SUCCESS_CODE)) {
                String result = body.getString("result");
                return success(result);
            } else {
                log.info("调用短链生成服务失败，状态码{}，响应体{}", response.getStatusCodeValue(), response.getBody());
            }
        }
        log.info("调用短链生成服务失败，状态码{}，响应体{}", response.getStatusCodeValue(), response.getBody());
        return error(ErrorCodeConstants.SMS_SHORT_LINK_FETCH_FAIL);
    }

    @Override
    public CommonResult<String> genJaguarLink(String path, String query) {
//        GenUrlLinkReqDto param = new GenUrlLinkReqDto();
//        param.setPath(path);
//        param.setQuery(query);
//        return shorLinkByWechatApi.genShortLink(param);
        log.info("捷豹短链生成入参, path:{}, query:{}", path, query);
        String request = path;
        if (StringUtils.isNotBlank(query)) {
            request = path +  "?" + query;
        }
        log.info("捷豹短链生成, request:{}", request);
        return genShortLink(request);
    }
}
