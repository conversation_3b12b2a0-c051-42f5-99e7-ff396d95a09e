package com.jlr.ecp.notification.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.executor.api.ScheduleJobService;
import com.jlr.ecp.executor.domain.ScheduleJobParamsDTO;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.notification.constant.BusinessConstants;
import com.jlr.ecp.notification.constant.Constants;
import com.jlr.ecp.notification.dal.dataobject.task.NotificationTask;
import com.jlr.ecp.notification.dal.dataobject.task.TaskModifyLog;
import com.jlr.ecp.notification.dal.dataobject.template.MessageTemplate;
import com.jlr.ecp.notification.dal.mysql.task.NotificationTaskMapper;
import com.jlr.ecp.notification.dal.mysql.task.TaskModifyMapper;
import com.jlr.ecp.notification.dal.repository.MessageTaskRepository;
import com.jlr.ecp.notification.dal.repository.MessageTemplateRepository;
import com.jlr.ecp.notification.dal.repository.TaskModifyRepository;
import com.jlr.ecp.notification.enums.*;
import com.jlr.ecp.notification.enums.task.*;
import com.jlr.ecp.notification.enums.template.SmsTemplateTypeEnum;
import com.jlr.ecp.notification.excel.service.NotificationExcelService;
import com.jlr.ecp.notification.req.task.*;
import com.jlr.ecp.notification.service.manual.SmsManualTaskService;
import com.jlr.ecp.notification.service.manual.ManualTaskSendService;
import com.jlr.ecp.notification.util.OperatorUtil;
import com.jlr.ecp.notification.util.PIPLDataUtil;
import com.jlr.ecp.notification.util.sms.TimeFormatUtil;
import com.jlr.ecp.notification.vo.task.*;
import com.jlr.ecp.system.enums.business.BusinessIdEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.jlr.ecp.framework.common.util.collection.CollectionUtils.*;

/**
 *  短信通知任务实现类
 *
 * <AUTHOR>
 * */
@Service
@Slf4j
public class SmsTaskServiceImpl implements SmsManualTaskService {
    @Resource
    private NotificationTaskMapper taskMapper;

    @Resource
    private MessageTemplateRepository templateRepository;

    @Resource
    private MessageTaskRepository taskRepository;

    @Resource
    private TaskModifyMapper taskModifyMapper;

    @Resource
    Snowflake ecpIdUtil;

    @Resource
    ScheduleJobService scheduleJobService;

    @Resource
    ManualTaskSendService manualTaskSendService;

    @Resource
    private NotificationExcelService excelService;

    @Resource
    private PIPLDataUtil piplDataUtil;

    @Resource(name = "asyncThreadPool")
    private ThreadPoolTaskExecutor asyncThreadPool;

    @Resource
    private TaskModifyRepository taskModifyRepository;

    @Resource
    private ApplicationContext applicationContext;


    private static final String REALTIME_SEND = "实时";


    private static final String TIME_FORMATTER = "yyyy-MM-dd HH:mm";


    /**
     * 返回手动任务的列表页面
     * @param taskPageListReq 任务查询参数
     * @return  PageResult<ManualTaskPageListVo>
     */
    @Override
    public PageResult<ManualTaskPageListVo> getManualTaskPageList(TaskPageListReq taskPageListReq) {
        log.info("返回手动任务的列表页面, taskPageListReq:{}", taskPageListReq);
        Page<NotificationTask> taskPage = queryTaskPageList(taskPageListReq);
        if (CollUtil.isEmpty(taskPage.getRecords())) {
            log.info("返回手动任务的列表页面,查询结果为空,taskPageListReq:{}", taskPageListReq);
            return new PageResult<>();
        }
        List<NotificationTask> list = taskPage.getRecords();
        // 查询模板类型信息
        Map<String, String> templateMap = Collections.emptyMap();
        Set<String> templateCodes = convertSet(list, e -> e.getMessageTemplateCode(), e -> StrUtil.isNotBlank(e.getMessageTemplateCode()));
        if (CollUtil.isNotEmpty(templateCodes)) {
            List<MessageTemplate> messageTemplates = templateRepository.queryTemplateListByCodes(templateCodes);
            templateMap = messageTemplates.stream().collect(Collectors.toMap(e -> e.getTemplateCode(), e -> e.getTemplateContent()));
        }

        ArrayList<ManualTaskPageListVo> resultList = new ArrayList<>(list.size());
        for (NotificationTask notificationTask : list) {
            ManualTaskPageListVo result = BeanUtil.copyProperties(notificationTask, ManualTaskPageListVo.class);
            result.setSignBrandType(notificationTask.getSignBrandText());
            result.setSendChannelType(notificationTask.getSendChannel());
            if (templateMap.containsKey(notificationTask.getMessageTemplateCode())) {
                result.setTemplateContent(templateMap.get(notificationTask.getMessageTemplateCode()));
            }
            if (BusinessConstants.TASK_SEND_TYPE.TIMING.equals(notificationTask.getTaskSendType())){
                result.setTaskSendTime(notificationTask.getTaskSendScheduleTime());
            }else if (BusinessConstants.TASK_SEND_TYPE.REAL_TIME.equals(notificationTask.getTaskSendType())){
                result.setTaskSendTime(TimeFormatUtil.localDateToStringWithoutSecond(notificationTask.getTaskSendRealTime()));
            }
            // 是否为草稿
            if(Objects.equals(BusinessConstants.WHETHER.YES, notificationTask.getDraftVersion())){
                result.setStatus(Constants.DRAFT);
            }else {
                result.setStatus(getTaskStatus(notificationTask.getStatus()));
            }
            resultList.add(result);
        }
        return new PageResult<>(resultList, taskPage.getTotal());
    }

    /**
     * 获取手动任务状态视图对象列表。
     *
     * @return 包含任务状态码和描述信息的视图对象列表
     */
    @Override
    public List<TaskStatusVO> getManualTaskStatusVO() {
        List<TaskStatusVO> resp = new ArrayList<>();
        for (TaskStatusEnum taskStatusEnum : TaskStatusEnum.values()) {
            TaskStatusVO taskStatusVO = TaskStatusVO.builder()
                    .status(taskStatusEnum.getStatus())
                    .desc(taskStatusEnum.getDesc())
                    .build();
            resp.add(taskStatusVO);
        }
        return resp;
    }

    /**
     *  任务编辑历史分页查询
     * @param taskHistoryReq 编辑任务请求入参
     * @return PageResult<TaskHistoryPageListVo>
     * */
    @Override
    public PageResult<TaskHistoryPageListVo> getTaskHistoryPageList(TaskHistoryReq taskHistoryReq) {
        Page<TaskModifyLog> historyPage = queryTaskHistoryPageList(taskHistoryReq);
        List<TaskModifyLog> taskHistoryList = historyPage.getRecords();
        List<TaskHistoryPageListVo> historyPageListVos = buildTaskHistoryPageList(taskHistoryList);
        return new PageResult<>(historyPageListVos, historyPage.getTotal());
    }


    /**
     * 分页查询编辑发送任务历史记录
     * @param taskHistoryReq 发送任务请求入参
     * @return  Page<NotificationHistory>
     * */
    public Page<TaskModifyLog> queryTaskHistoryPageList(TaskHistoryReq taskHistoryReq) {
        Page<TaskModifyLog> page = new Page<>(taskHistoryReq.getPageNo(), taskHistoryReq.getPageSize());
        LambdaQueryWrapper<TaskModifyLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(taskHistoryReq.getTaskCode()),
                        TaskModifyLog::getTaskCode, taskHistoryReq.getTaskCode())
                .eq(TaskModifyLog::getIsDeleted, IsDeleteEnum.NO.getStatus())
                .orderByDesc(TaskModifyLog::getOperateTime);
        return taskModifyMapper.selectPage(page, wrapper);
    }

    @Override
    public CommonResult<ManualTaskDetailVo> getManualTaskDetail(TaskDetailReq taskDetailReq) {
        ManualTaskDetailVo taskDetailVo = new ManualTaskDetailVo();
        NotificationTask notificationTask = taskRepository.queryTaskByCode(taskDetailReq.getTaskCode());
        if (Objects.isNull(notificationTask)) {
            log.info("任务详情页查询, 查询任务为空, taskDetailReq:{}", taskDetailReq);
            return CommonResult.success(taskDetailVo);
        }
        taskDetailVo.setTaskCode(notificationTask.getTaskCode());
        taskDetailVo.setTaskName(notificationTask.getTaskName());
        taskDetailVo.setTriggerAction(notificationTask.getTriggerAction());
        if (BusinessConstants.TASK_SEND_TYPE.TIMING.equals(notificationTask.getTaskSendType())){
            taskDetailVo.setTaskSendScheduleTime(notificationTask.getTaskSendScheduleTime());
        }else if (BusinessConstants.TASK_SEND_TYPE.REAL_TIME.equals(notificationTask.getTaskSendType())){
            taskDetailVo.setTaskSendScheduleTime(TimeFormatUtil.localDateToStringWithoutSecond(notificationTask.getTaskSendRealTime()));
        }
        taskDetailVo.setStatus(notificationTask.getStatus());
        taskDetailVo.setTaskSendType(notificationTask.getTaskSendType());
        taskDetailVo.setMessageFile(piplDataUtil.getEncryptText(notificationTask.getMessageFile()));
        taskDetailVo.setErrorMsgFile(notificationTask.getErrorMsgFile());
        taskDetailVo.setSignBrandType(notificationTask.getSignBrandText());
        taskDetailVo.setSendChannelType(notificationTask.getSendChannel());
        // 取文件名
        if(StrUtil.isNotBlank(notificationTask.getMessageFile())){
            int index = notificationTask.getMessageFile().lastIndexOf("/");
            taskDetailVo.setFileName(notificationTask.getMessageFile().substring(index+1));
            taskDetailVo.setCheckStatus(true);
            taskDetailVo.setCheckResult(true);
        }
        if(StrUtil.isNotBlank(taskDetailVo.getErrorMsgFile())){
            int index = taskDetailVo.getErrorMsgFile().lastIndexOf("/");
            taskDetailVo.setFileName(taskDetailVo.getErrorMsgFile().substring(index+1));
            taskDetailVo.setCheckStatus(true);
            taskDetailVo.setCheckResult(false);
        }
        taskDetailVo.setSubmitFileType(notificationTask.getSubmitFileType());

        // 模板内容字段填充
        MessageTemplate messageTemplate = templateRepository.getMessageTemplateByCode(notificationTask.getMessageTemplateCode());
        if (Objects.nonNull(messageTemplate)) {
            taskDetailVo.setTemplateCode(messageTemplate.getTemplateCode());
            taskDetailVo.setTemplateName(messageTemplate.getTemplateName());
            taskDetailVo.setTemplateType(String.valueOf(messageTemplate.getTemplateType()));
            taskDetailVo.setTemplateContent(messageTemplate.getTemplateContent());
        }
        return CommonResult.success(taskDetailVo);
    }


    /**
     * 编辑手动发送任务禁用
     * @param modifyStatusReq 任务状态编辑
     * @return CommonResult<Boolean>
     * */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<Boolean> modifyManualTaskStatus(TaskModifyStatusReq modifyStatusReq) {
        log.info("编辑发送任务禁用, modifyStatusReq:{}", JSON.toJSONString(modifyStatusReq));

        CommonResult<Boolean> taskCheck = checkTaskArg(modifyStatusReq);
        if (!taskCheck.isSuccess()) {
            return taskCheck;
        }
        NotificationTask notificationTask = taskRepository.queryTaskByCode(modifyStatusReq.getTaskCode());
        if (Objects.isNull(notificationTask)) {
            return CommonResult.error(ErrorCodeConstants.TASK_NO_EXIST);
        }

        // 保存旧状态用于日志记录
        NotificationTask oldTaskDO = new NotificationTask();
        BeanUtils.copyProperties(notificationTask, oldTaskDO);
        log.info("编辑发送任务禁用, oldTaskDO:{}", JSON.toJSONString(oldTaskDO));

        updateTaskStatus(notificationTask, modifyStatusReq);
        addManualTaskModifyStatusLog(oldTaskDO, modifyStatusReq);
        return CommonResult.success(true);
    }

    /**
     * 启用手动通知任务并进行发送
     * @param modifyStatusReq 任务状态编辑
     * @return CommonResult<Boolean>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> openManualTask(TaskModifyStatusReq modifyStatusReq) {
        log.info("启用手动通知任务并进行发送, modifyStatusReq:{}", JSON.toJSONString(modifyStatusReq));

        CommonResult<Boolean> taskCheck = checkTaskArg(modifyStatusReq);
        if (!taskCheck.isSuccess()) {
            return taskCheck;
        }
        NotificationTask notificationTask = taskRepository.queryTaskByCode(modifyStatusReq.getTaskCode());
        if (Objects.isNull(notificationTask)) {
            return CommonResult.error(ErrorCodeConstants.TASK_NO_EXIST);
        }

        // 保存旧状态用于日志记录
        NotificationTask oldTaskDO = new NotificationTask();
        BeanUtils.copyProperties(notificationTask, oldTaskDO);
        log.info("启用手动通知任务并进行发送, oldTaskDO:{}", JSON.toJSONString(oldTaskDO));

        // 校验手机号文件是否存在
        Assert.notBlank(notificationTask.getMessageFile(), ()-> new ServiceException(ErrorCodeConstants.TASK_CANNOT_OPEN));
        // 定时发送校验时间
        if (BusinessConstants.TASK_SEND_TYPE.TIMING.equals(notificationTask.getTaskSendType())){
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime sendTime = DateUtil.parseLocalDateTime(
                    notificationTask.getTaskSendScheduleTime(),TIME_FORMATTER);
            // 还有三分钟就执行通知任务
            if (sendTime.isBefore(now)) {
                return CommonResult.error(ErrorCodeConstants.TASK_EXPIRED);
            }
            if (sendTime.minusMinutes(3).isBefore(now)) {
                return CommonResult.error(ErrorCodeConstants.TASK_EXPIRED_REMAIN_THREE_MINUTES);
            }
        }
        updateTaskStatus(notificationTask, modifyStatusReq);
        addManualTaskModifyStatusLog(oldTaskDO, modifyStatusReq);
        // 调用靖宇的接口进行后置任务触发
        if (BusinessConstants.TASK_SEND_TYPE.TIMING.equals(notificationTask.getTaskSendType())){
            ScheduleJobParamsDTO jobParams = new ScheduleJobParamsDTO();
            String taskSendScheduleTime = notificationTask.getTaskSendScheduleTime();
            // 0 10 6 15 8 ? 2023  表示 2023年8月15日6点10分0秒
            LocalDateTime sendDate = DateUtil.parseLocalDateTime(taskSendScheduleTime, TIME_FORMATTER);
            String cronStr = sendDate.format(DateTimeFormatter.ofPattern("ss mm HH dd MM ? yyyy"));
            jobParams.setCron(cronStr);
            // 靖宇说就传task_code
            jobParams.setJobParam(modifyStatusReq.getTaskCode());
            scheduleJobService.createManualSendTask(jobParams);
        }else if (BusinessConstants.TASK_SEND_TYPE.REAL_TIME.equals(notificationTask.getTaskSendType())){
            // 德华说就传task_code
            CompletableFuture.runAsync(() -> manualTaskSendService.sendTask(modifyStatusReq.getTaskCode()), asyncThreadPool);
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> deleteFile(String taskCode) {
        NotificationTask task = taskRepository.queryTaskByCode(taskCode);
        if (Objects.isNull(task)) {
            return CommonResult.error(ErrorCodeConstants.MANUAL_TASK_NOT_EXIST);
        }
        if (TaskStatusEnum.START.getStatus().equals(task.getStatus())) {
            return CommonResult.error(ErrorCodeConstants.MANUAL_TASK_STATUS_NOT_STOP);
        }
        // 置空文件url
        String file = task.getMessageFile();
        String errorMsgFile = task.getErrorMsgFile();
        task.setErrorMsgFile(null);
        task.setMessageFile(null);
        taskMapper.updateById(task);
        // 删除s3文件
        if(StrUtil.isNotBlank(file)){
            excelService.deleteExcel(file);
        }
        if(StrUtil.isNotBlank(errorMsgFile)){
            excelService.deleteExcel(errorMsgFile);
        }
        return CommonResult.success(true);
    }

    @Override
    public ManualTaskHistoryDetailVO getManualTaskModifyDetail(Long logId) {
        TaskModifyLog logDO = taskModifyRepository.getById(logId);
        if (logDO == null) {
            log.info("手动任务修改记录不存在, logId:{}", logId);
            return null;
        }

        ManualTaskHistoryDetailVO vo = new ManualTaskHistoryDetailVO();
        BeanUtils.copyProperties(logDO, vo);

        // 如果需要解析JSON字段，可以取消注释以下代码
        /*
        JSONObject oldJson = JSONUtil.parseObj(logDO.getModifyFieldOldValue());
        JSONObject newJson = JSONUtil.parseObj(logDO.getModifyFieldNewValue());
        List<ManualTaskHistoryDetailVO.FieldChange> changes = new ArrayList<>();
        oldJson.forEach((key, oldValue) -> {
            String newValue = newJson.getStr(key);
            changes.add(new ManualTaskHistoryDetailVO.FieldChange(key, oldValue.toString(), newValue));
        });
        vo.setChanges(changes);
        */
        return vo;
    }

    /**
     * 批量启动手动任务处理
     *
     * @param batchStatusReq 批量任务状态修改请求参数
     * @return CommonResult<String> 操作结果包装对象
     */
    @Override
    public CommonResult<String> batchStartManualTask(BatchTaskModifyStatusReq batchStatusReq) {
        List<TaskModifyStatusReq> modifyStatusReqList = batchStatusReq.getTaskModifyStatusReqList();
        if (CollUtil.isEmpty(modifyStatusReqList)) {
            return CommonResult.success("批量启动手动任务处理, 要开启的任务数量为空");
        }
        log.info("批量启动手动任务处理, modifyStatusReqList数量:{}, batchOperationStatus:{}",
                modifyStatusReqList.size(), batchStatusReq.getBatchOperationStatus());
        int success = 0;
        int fail = 0;
        SmsTaskServiceImpl manualTaskService = applicationContext.getBean(getClass());
        for (TaskModifyStatusReq taskModifyStatusReq : modifyStatusReqList) {
            taskModifyStatusReq.setModifyStatus(BusinessConstants.WHETHER.YES);
            CommonResult<Boolean> resp = manualTaskService.openManualTask(taskModifyStatusReq);
            if (resp.isSuccess() || Boolean.TRUE.equals(resp.getData())) {
                success++;
            } else {
                fail++;
            }
        }
        String resp = success + "条启用成功";
        String errorMsg = fail + "条启用失败";
        if (success == modifyStatusReqList.size()) {
            return CommonResult.success(resp);
        } else if (fail == modifyStatusReqList.size()) {
            return CommonResult.error(168987312, errorMsg);
        } else {
            resp += "，" + errorMsg;
        }
        return CommonResult.error(168987312, resp);
    }

    /**
     * 批量停用手动任务操作接口
     *
     * @param batchStatusReq 批量任务状态修改请求参数
     * @return CommonResult<String> 操作结果封装对象
     */
    @Override
    public CommonResult<String> batchStopManualTask(BatchTaskModifyStatusReq batchStatusReq) {
        List<TaskModifyStatusReq> modifyStatusReqList = batchStatusReq.getTaskModifyStatusReqList();
        if (CollUtil.isEmpty(modifyStatusReqList)) {
            return CommonResult.success("批量停用手动任务处理, 要停用的任务数量为空");
        }
        log.info("批量停用手动任务处理, modifyStatusReqList数量:{}, batchOperationStatus:{}",
                modifyStatusReqList.size(), batchStatusReq.getBatchOperationStatus());
        int success = 0;
        int fail = 0;
        SmsTaskServiceImpl manualTaskService = applicationContext.getBean(getClass());
        for (TaskModifyStatusReq taskModifyStatusReq : modifyStatusReqList) {
            taskModifyStatusReq.setModifyStatus(BusinessConstants.WHETHER.NOT);
            CommonResult<Boolean> resp = manualTaskService.modifyManualTaskStatus(taskModifyStatusReq);
            if (resp.isSuccess() || Boolean.TRUE.equals(resp.getData())) {
                success++;
            } else {
                fail++;
            }
        }
        String resp = success + "条停用成功";
        String errorMsg = fail + "条停用失败";
        if (success == modifyStatusReqList.size()) {
            return CommonResult.success(resp);
        } else if (fail == modifyStatusReqList.size()) {
            return CommonResult.error(168987389, errorMsg);
        } else {
            resp += "，" + errorMsg;
        }
        return CommonResult.error(168987389, resp);
    }

    /**
     * 校验编辑任务状态的参数
     * @param statusReq 任务状态入参
     * @return CommonResult<Boolean>
     * */
    public CommonResult<Boolean> checkTaskArg(TaskModifyStatusReq statusReq) {
        if (Objects.isNull(statusReq)) {
            return CommonResult.error(ErrorCodeConstants.TASK_REQ_NULL);
        }
        if (StringUtils.isBlank(statusReq.getTaskCode())) {
            return CommonResult.error(ErrorCodeConstants.TASK_CODE_NULL);
        }
        if (Objects.isNull(statusReq.getModifyStatus())) {
            return CommonResult.error(ErrorCodeConstants.TASK_STATUS_NULL);
        }
        if (!statusReq.getModifyStatus().equals(0) && !statusReq.getModifyStatus().equals(1)) {
            return CommonResult.error(ErrorCodeConstants.TASK_STATUS_ERROR);
        }
        NotificationTask notificationTask = taskRepository.queryTaskByCode(statusReq.getTaskCode());
        if (Objects.nonNull(notificationTask)
                && TaskStatusEnum.FINISH_SEND.getStatus().equals(notificationTask.getStatus())) {
            return CommonResult.error(ErrorCodeConstants.TASK_STATUS_FINISH_SEND);
        }
        return CommonResult.success(true);
    }

    /**
     *  更新任务状态
     * @param notificationTask 发送任务
     * @param modifyStatusReq 修改的状态入参
     * */
    public void updateTaskStatus(NotificationTask notificationTask, TaskModifyStatusReq modifyStatusReq) {
        if (Objects.isNull(notificationTask) || Objects.isNull(modifyStatusReq)) {
            log.info("发送任务或修改任务入参为空");
            return ;
        }
        notificationTask.setStatus(modifyStatusReq.getModifyStatus());
        LocalDateTime now = LocalDateTime.now();
        //当修改状态为禁用时, 修改禁用时间为当前
        if (TaskStatusEnum.STOP.getStatus().equals(modifyStatusReq.getModifyStatus())) {
            notificationTask.setDeactivateTime(now);
        }
        //当修改状态为启用时，修改启用时间为当前,停用时间清空
        if (TaskStatusEnum.START.getStatus().equals(modifyStatusReq.getModifyStatus())) {
            notificationTask.setActivateTime(now);
            notificationTask.setDeactivateTime(null);
            if (BusinessConstants.TASK_SEND_TYPE.REAL_TIME.equals(notificationTask.getTaskSendType())){
                // 启用时，给发送时间赋值
                notificationTask.setTaskSendRealTime(now);
            }
        }
        notificationTask.setUpdatedBy(null);
        notificationTask.setUpdatedTime(null);
        taskMapper.updateById(notificationTask);
    }

    /**
     *  手动任务添加启用、禁用状态的日志
     * */
    public void addManualTaskModifyStatusLog(NotificationTask oldTask, TaskModifyStatusReq newTask) {
        Integer oldStatus = oldTask.getStatus();
        Integer newStatus = newTask.getModifyStatus();

        if (Objects.equals(oldStatus, newStatus)) {
            return; // 状态未变化不记录
        }

        Map<String, Object> oldValues = new HashMap<>();
        Map<String, Object> newValues = new HashMap<>();

        String fieldName = ManualNotificationTaskFieldEnum.EDIT_STATUS.getFieldName();
        oldValues.put(fieldName, TaskStatusEnum.getTaskStatusEnumByCode(oldStatus).getDesc());
        newValues.put(fieldName, TaskStatusEnum.getTaskStatusEnumByCode(newStatus).getDesc());

        TaskModifyLog log = TaskModifyLog.builder()
                .taskCode(oldTask.getTaskCode())
                .modifyModule(ManualNotificationTaskFieldEnum.EDIT_STATUS.getModuleName())
                .modifyFieldCount(1)
                .modifyFieldOldValue(JSON.toJSONString(oldValues))
                .modifyFieldNewValue(JSON.toJSONString(newValues))
                .operateUser(OperatorUtil.getOperator())
                .operateTime(LocalDateTime.now())
                .build();

        taskModifyMapper.insert(log);
    }

    /**
     * 任务编辑类型
     * @param modifyType 修改类型
     * @return String
     * */
    public String getTaskModifyContent(Integer modifyType) {
        TaskModifyTypeEnum modifyTypeEnum = TaskModifyTypeEnum.getTaskModifyTypeEnumByType(modifyType);
        if (Objects.isNull(modifyTypeEnum)) {
            log.info("按照任务编辑类型返回任务修改枚举为空, taskModifyType:{}", modifyType);
            return "";
        }
        return modifyTypeEnum.getDesc();
    }

    /**
     * 手动任务编辑类型
     * @param modifyType 修改类型
     * @return String
     * */
    public String getManualTaskModifyContent(Integer modifyType) {
        if (BusinessConstants.WHETHER.YES.equals(modifyType)) {
            return "启用手动通知任务";
        }
        if (BusinessConstants.WHETHER.NOT.equals(modifyType)) {
            return "停用手动通知任务";
        }
        return StrUtil.EMPTY;
    }



    /**
     *  批量构建发送任务的分页列表页
     * @param taskHistoryList 发送任务列表页
     * @return  List<TaskHistoryPageListVo>
     * */
    public List<TaskHistoryPageListVo> buildTaskHistoryPageList(List<TaskModifyLog> taskHistoryList) {
        List<TaskHistoryPageListVo> pageListVos = new ArrayList<>();
        if (CollectionUtils.isEmpty(taskHistoryList)) {
            return pageListVos;
        }
        for (TaskModifyLog pageListVo : taskHistoryList) {
            pageListVos.add(buildTaskHistoryPageVo(pageListVo));
        }
        return pageListVos;
    }

    /**
     *  构建任务编辑历史记录VO
     * @param taskHistory 发送任务编辑历史
     * */
    public TaskHistoryPageListVo buildTaskHistoryPageVo(TaskModifyLog taskHistory) {
        if (Objects.isNull(taskHistory)) {
            return TaskHistoryPageListVo.builder().build();
        }

        TaskHistoryPageListVo vo = new TaskHistoryPageListVo();
        BeanUtils.copyProperties(taskHistory, vo);


        // 获取所有需要显示详情的字段
        Set<String> allDetailFields = ManualNotificationTaskFieldEnum.getAllDetailFields();

        // set setModifyField
        String oldValue = taskHistory.getModifyFieldOldValue();
        if(StrUtil.isNotBlank(oldValue)){
            Set<String> oldSet = JSON.parseObject(oldValue, Feature.OrderedField).keySet();
            vo.setModifyField(String.join("、", oldSet));
            // set modifyDetail 是否显示 修改详情
            boolean modifyDetail = oldSet.stream().anyMatch(allDetailFields::contains);
            vo.setModifyDetail(modifyDetail);
        }

        return vo;
    }

    /**
     * 任务模板类型
     * @param typeCode 模板类型code
     * @return String
     * */
    public String getTaskTemplateType(Integer typeCode) {
        SmsTemplateTypeEnum typeEnum = SmsTemplateTypeEnum.getTemplateByCode(typeCode);
        if (Objects.isNull(typeEnum)) {
            log.error("短信模板类型不存在，smsTemplateTypeCode:{}", typeCode);
            return "";
        }
        return typeEnum.getDesc();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> addManualTask(TaskCreatReq taskCreatReq) {
        checkAdd(taskCreatReq);
        NotificationTask insertBO = createManualTaskDo(taskCreatReq);
        taskMapper.insert(insertBO);
        return CommonResult.success(true);
    }

    /**
     * 创建手动任务的实现方法。
     *
     * @param taskCreatReq 包含任务创建需求的请求对象，包括任务名称、发送类型、消息模板代码等信息。
     * @return 返回一个准备插入数据库的手动任务对象，包含了任务的各种属性设置。
     */
    private NotificationTask createManualTaskDo(TaskCreatReq taskCreatReq) {
        NotificationTask insertBO = new NotificationTask();
        insertBO.setBusinessCode(BusinessIdEnum.VCS.getCode());
        insertBO.setTaskCode(ecpIdUtil.nextIdStr());
        insertBO.setTaskName(taskCreatReq.getTaskName());
        insertBO.setTriggerAction("手动任务");
        insertBO.setTaskSendType(taskCreatReq.getTaskSendType());
        insertBO.setMessageTemplateCode(taskCreatReq.getMessageTemplateCode());
        insertBO.setTaskType(TaskTypeEnum.MANUAL.getType());
        insertBO.setSubmitFileType(taskCreatReq.getSubmitFileType());
        if (BusinessEnum.OperationType.DRAFT_ONLY.getType().equals(taskCreatReq.getAddDataType())) {
            insertBO.setDraftVersion(BusinessConstants.WHETHER.YES);
            insertBO.setStatus(TaskStatusEnum.STOP.getStatus());
        } else if (BusinessEnum.OperationType.SAVE_AND_SUBMIT.getType().equals(taskCreatReq.getAddDataType())) {
            insertBO.setDraftVersion(BusinessConstants.WHETHER.NOT);
            insertBO.setStatus(TaskStatusEnum.TO_BE_ACTIVATED.getStatus());
        }
        if (BusinessConstants.TASK_SEND_TYPE.TIMING.equals(taskCreatReq.getTaskSendType())) {
            insertBO.setTaskSendScheduleTime(taskCreatReq.getSendTime());
        }
        insertBO.setSignBrandText(taskCreatReq.getSignBrandType());
        insertBO.setSendChannel(taskCreatReq.getSendChannelType());
        insertBO.setMessageFile(piplDataUtil.getDecodeText(taskCreatReq.getMessageFile()));
        insertBO.setErrorMsgFile(taskCreatReq.getErrorMsgFile());
        return insertBO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> updateManualTask(TaskModifyReq taskModifyReq) {
        log.info("手动任务编辑日志入参, taskModifyReq:{}", taskModifyReq);
        NotificationTask taskExist = checkUpdate(taskModifyReq);

        NotificationTask oldTask = new NotificationTask();
        BeanUtils.copyProperties(taskExist, oldTask);
        log.info("已存在的手动任务记录 oldTask:{}", oldTask);

        taskExist.setTaskName(taskModifyReq.getTaskName());
        taskExist.setTaskSendType(taskModifyReq.getTaskSendType());
        taskExist.setMessageTemplateCode(taskModifyReq.getMessageTemplateCode());
        taskExist.setTaskType(TaskTypeEnum.MANUAL.getType());
        taskExist.setSubmitFileType(taskModifyReq.getSubmitFileType());
        if (BusinessEnum.OperationType.DRAFT_ONLY.getType().equals(taskModifyReq.getAddDataType())) {
            taskExist.setDraftVersion(BusinessConstants.WHETHER.YES);
            taskExist.setStatus(TaskStatusEnum.STOP.getStatus());
        } else if (BusinessEnum.OperationType.SAVE_AND_SUBMIT.getType().equals(taskModifyReq.getAddDataType())) {
            taskExist.setDraftVersion(BusinessConstants.WHETHER.NOT);
            taskExist.setStatus(TaskStatusEnum.TO_BE_ACTIVATED.getStatus());
        }
        if (BusinessConstants.TASK_SEND_TYPE.REAL_TIME.equals(taskModifyReq.getTaskSendType())) {
            taskExist.setTaskSendScheduleTime(null);
        } else if (BusinessConstants.TASK_SEND_TYPE.TIMING.equals(taskModifyReq.getTaskSendType())) {
            taskExist.setTaskSendScheduleTime(taskModifyReq.getSendTime());
        }
        taskExist.setMessageFile(piplDataUtil.getDecodeText(taskModifyReq.getMessageFile()));
        taskExist.setErrorMsgFile(taskModifyReq.getErrorMsgFile());
        taskExist.setUpdatedBy(null);
        taskExist.setUpdatedTime(null);
        taskExist.setSignBrandText(taskModifyReq.getSignBrandType());
        taskExist.setSendChannel(taskModifyReq.getSendChannelType());
        taskMapper.updateById(taskExist);

        // 记录操作日志（排除通知状态模块）
        recordEditLogs(oldTask, taskModifyReq);
        return CommonResult.success(true);
    }

    // 新增recordEditLogs方法
    private void recordEditLogs(NotificationTask oldTask,
                                TaskModifyReq req) {
        log.info("手动任务编辑日志记录, oldTask:{}, req:{}", JSON.toJSONString(oldTask), JSON.toJSONString(req));

        // 记录任务概览模块
        checkAndLogTaskOverview(oldTask, req);

        // 记录接收号码模块
        checkAndLogReceiverConfig(oldTask, req);

        // 记录模板信息模块
        checkAndLogTemplateInfo(oldTask, req);
    }

    // 任务概览模块日志记录
    private void checkAndLogTaskOverview(NotificationTask oldTask,
                                         TaskModifyReq req) {
        Map<String, Object> oldValues = new HashMap<>();
        Map<String, Object> newValues = new HashMap<>();
        AtomicInteger modifyCount = new AtomicInteger(0);

        // 任务名称
        if (!Objects.equals(oldTask.getTaskName(), req.getTaskName())) {
            modifyCount.incrementAndGet();
            oldValues.put(ManualNotificationTaskFieldEnum.EDIT_TASK_NAME.getFieldName(), oldTask.getTaskName());
            newValues.put(ManualNotificationTaskFieldEnum.EDIT_TASK_NAME.getFieldName(), req.getTaskName());
        }

        // 发送时间
        String oldSendTime = buildSendTimeDescription(oldTask);
        String newSendTime = buildSendTimeDescription(req);
        if (!Objects.equals(oldSendTime, newSendTime)) {
            modifyCount.incrementAndGet();
            oldValues.put(ManualNotificationTaskFieldEnum.EDIT_SEND_TIME.getFieldName(), oldSendTime);
            newValues.put(ManualNotificationTaskFieldEnum.EDIT_SEND_TIME.getFieldName(), newSendTime);
        }

        insertModuleLog(req.getTaskCode(), ManualNotificationTaskFieldEnum.EDIT_TASK_NAME,
                modifyCount.get(), oldValues, newValues);
    }

    private void checkAndLogReceiverConfig(NotificationTask oldTask,
                                           TaskModifyReq req) {
        Map<String, Object> oldValues = new HashMap<>();
        Map<String, Object> newValues = new HashMap<>();
        AtomicInteger modifyCount = new AtomicInteger(0);

        // 接收号码形式
        if (!Objects.equals(oldTask.getSubmitFileType(), req.getSubmitFileType())) {
            modifyCount.incrementAndGet();
            oldValues.put(ManualNotificationTaskFieldEnum.EDIT_RECEIVER_TYPE.getFieldName(),
                    UploadFileDataTypeEnum.fromCode(oldTask.getSubmitFileType()).getDescription());
            newValues.put(ManualNotificationTaskFieldEnum.EDIT_RECEIVER_TYPE.getFieldName(),
                    UploadFileDataTypeEnum.fromCode(req.getSubmitFileType()).getDescription());
        }

        // 文件上传（无修改详情）
        if (!Objects.equals(oldTask.getMessageFile(), piplDataUtil.getDecodeText(req.getMessageFile()))) {
            modifyCount.incrementAndGet();
            oldValues.put(ManualNotificationTaskFieldEnum.UPLOAD_FILE.getFieldName(), "");
            newValues.put(ManualNotificationTaskFieldEnum.UPLOAD_FILE.getFieldName(), "");
        }

        insertModuleLog(req.getTaskCode(), ManualNotificationTaskFieldEnum.EDIT_RECEIVER_TYPE,
                modifyCount.get(), oldValues, newValues);
    }

    private void checkAndLogTemplateInfo(NotificationTask oldTask,
                                         TaskModifyReq req) {
        Map<String, Object> oldValues = new HashMap<>();
        Map<String, Object> newValues = new HashMap<>();
        AtomicInteger modifyCount = new AtomicInteger(0);

        // 通知签名
        if (!Objects.equals(oldTask.getSignBrandText(), req.getSignBrandType())) {
            modifyCount.incrementAndGet();
            oldValues.put(ManualNotificationTaskFieldEnum.EDIT_SIGNATURE.getFieldName(),
                    SignBrandTextEnum.getSignDescBySignCode(oldTask.getSignBrandText()));
            newValues.put(ManualNotificationTaskFieldEnum.EDIT_SIGNATURE.getFieldName(),
                    SignBrandTextEnum.getSignDescBySignCode(req.getSignBrandType()));
        }

        // 模板名称（通过模板code获取）
        String oldTemplateName = getTemplateName(oldTask.getMessageTemplateCode());
        String newTemplateName = getTemplateName(req.getMessageTemplateCode());
        if (!Objects.equals(oldTemplateName, newTemplateName)) {
            modifyCount.incrementAndGet();
            oldValues.put(ManualNotificationTaskFieldEnum.EDIT_TEMPLATE_NAME.getFieldName(), oldTemplateName);
            newValues.put(ManualNotificationTaskFieldEnum.EDIT_TEMPLATE_NAME.getFieldName(), newTemplateName);
        }

        // 通知性质
        if (!Objects.equals(oldTask.getSendChannel(), req.getSendChannelType())) {
            modifyCount.incrementAndGet();
            oldValues.put(ManualNotificationTaskFieldEnum.EDIT_NOTIFICATION_TYPE.getFieldName(),
                    SendChannelEnum.getNatureDescByChannelCode(oldTask.getSendChannel()));
            newValues.put(ManualNotificationTaskFieldEnum.EDIT_NOTIFICATION_TYPE.getFieldName(),
                    SendChannelEnum.getNatureDescByChannelCode(req.getSendChannelType()));
        }

        insertModuleLog(req.getTaskCode(), ManualNotificationTaskFieldEnum.EDIT_SIGNATURE,
                modifyCount.get(), oldValues, newValues);
    }

    private String getTemplateName(String messageTemplateCode) {
        MessageTemplate manualTemplateDetail = templateRepository.getManualTemplateDetail(messageTemplateCode);
        return Objects.nonNull(manualTemplateDetail) ? manualTemplateDetail.getTemplateName() : "";
    }

    private void insertModuleLog(String taskCode, ManualNotificationTaskFieldEnum fieldEnum,
                                 int modifyCount, Map<String, Object> oldVals, Map<String, Object> newVals) {
        if (modifyCount > 0) {
            TaskModifyLog log = buildModifyLog(taskCode, fieldEnum.getModuleName(),
                    modifyCount, oldVals, newVals);
            taskModifyMapper.insert(log);
        }
    }

    private String buildSendTimeDescription(NotificationTask task) {
        if (task.getTaskSendType() == 1) {
            return "审批后立即发送";
        } else {
            return String.format("定时%s", task.getTaskSendScheduleTime());
        }
    }

    private String buildSendTimeDescription(TaskModifyReq req) {
        if (req.getTaskSendType() == 1) {
            return "审批后立即发送";
        } else {
            return String.format("定时%s", req.getSendTime());
        }
    }

    // 日志构建方法
    private TaskModifyLog buildModifyLog(String taskCode, String module,
                                         int fieldCount, Map<String, Object> oldVals, Map<String, Object> newVals) {
        return TaskModifyLog.builder()
                .taskCode(taskCode)
                .modifyModule(module)
                .modifyFieldCount(fieldCount)
                .modifyFieldOldValue(JSON.toJSONString(oldVals))
                .modifyFieldNewValue(JSON.toJSONString(newVals))
                .operateUser(WebFrameworkUtils.getLoginUserName())
                .operateTime(LocalDateTime.now())
                .build();
    }

    @NotNull
    private NotificationTask checkUpdate(TaskModifyReq taskModifyReq) {
        if (BusinessConstants.TASK_SEND_TYPE.TIMING.equals(taskModifyReq.getTaskSendType())) {
            Assert.notBlank(taskModifyReq.getSendTime(), ()-> new ServiceException(ErrorCodeConstants.TASK_SEND_TIME_NULL));
            try {
                DateUtil.parseLocalDateTime(taskModifyReq.getSendTime(), TIME_FORMATTER);
            }catch (DateTimeParseException e){
                log.error("时间格式化错误, taskModifyReq={}", taskModifyReq, e);
                throw exception(ErrorCodeConstants.TASK_SEND_TIME_FORMAT_ERROR);
            }
        }
        if (TaskSendTypeEnum.SCHEDULED.getCode().equals(taskModifyReq.getTaskSendType()) &&
                StringUtils.isNotBlank(taskModifyReq.getSendTime())) {
            String nowTime = TimeFormatUtil.timeToStringByFormat(LocalDateTime.now(), TimeFormatUtil.formatter_4);
            if (taskModifyReq.getSendTime().compareTo(nowTime) <= 0) {
                throw exception(ErrorCodeConstants.MANUAL_TASK_SEND_TIME_ERROR);
            }
        }

        if (BusinessEnum.OperationType.SAVE_AND_SUBMIT.getType().equals(taskModifyReq.getAddDataType())) {
            if (Objects.isNull(taskModifyReq.getSignBrandType())) {
                throw exception(ErrorCodeConstants.MANUAL_TASK_SIGN_EMPTY);
            }
            if (Objects.isNull(taskModifyReq.getSendChannelType())) {
                throw exception(ErrorCodeConstants.MANUAL_TASK_CHANNEL_EMPTY);
            }
        }

        if (StrUtil.isNotBlank(taskModifyReq.getMessageTemplateCode())){
            boolean exists = templateRepository.existsTemplateCode(taskModifyReq.getMessageTemplateCode());
            // Assert.isTrue(exists, "选择的通知模板无效或不存在");
            Assert.isTrue(exists, ()-> new ServiceException(ErrorCodeConstants.TEMPLATE_NO_EXIST));
        }

        NotificationTask taskExist = taskMapper.selectOne(new LambdaQueryWrapperX<NotificationTask>()
                .eq(NotificationTask::getTaskCode, taskModifyReq.getTaskCode()));
        // Assert.notNull(taskExist, "要更新的手动任务无效或不存在");
        Assert.notNull(taskExist, ()-> new ServiceException(ErrorCodeConstants.TASK_NO_EXIST));

        Assert.isTrue(TaskStatusEnum.STOP.getStatus().equals(taskExist.getStatus())
                        || TaskStatusEnum.TO_BE_ACTIVATED.getStatus().equals(taskExist.getStatus()),
                // "已启用或已发送的任务禁止更新"
                () -> new ServiceException(ErrorCodeConstants.TASK_MODIFY_NOT_ALLOW)
        );
        if (BusinessEnum.OperationType.SAVE_AND_SUBMIT.getType().equals(taskModifyReq.getAddDataType())){
            Assert.notBlank(taskModifyReq.getMessageFile(), ()-> new ServiceException(ErrorCodeConstants.TASK_CANNOT_SUBMIT));
            Assert.notBlank(taskModifyReq.getMessageTemplateCode(), ()-> new ServiceException(ErrorCodeConstants.TEMPLATE_CODE_NULL));
        }
        return taskExist;
    }

    private void checkAdd(TaskCreatReq taskCreatReq) {
        if (BusinessConstants.TASK_SEND_TYPE.TIMING.equals(taskCreatReq.getTaskSendType())) {
            Assert.notBlank(taskCreatReq.getSendTime(), ()-> new ServiceException(ErrorCodeConstants.TASK_SEND_TIME_NULL));
            try {
                DateUtil.parseLocalDateTime(taskCreatReq.getSendTime(), TIME_FORMATTER);
            }catch (DateTimeParseException e){
                log.error("时间格式化错误, taskCreatReq={}", taskCreatReq, e);
                throw exception(ErrorCodeConstants.TASK_SEND_TIME_FORMAT_ERROR);
            }
        }
        if (TaskSendTypeEnum.SCHEDULED.getCode().equals(taskCreatReq.getTaskSendType()) &&
                StringUtils.isNotBlank(taskCreatReq.getSendTime())) {
            String nowTime = TimeFormatUtil.timeToStringByFormat(LocalDateTime.now(), TimeFormatUtil.formatter_4);
            if (taskCreatReq.getSendTime().compareTo(nowTime) <= 0) {
                throw exception(ErrorCodeConstants.MANUAL_TASK_SEND_TIME_ERROR);
            }
        }
        if (BusinessEnum.OperationType.SAVE_AND_SUBMIT.getType().equals(taskCreatReq.getAddDataType())) {
            if (Objects.isNull(taskCreatReq.getSignBrandType())) {
                throw exception(ErrorCodeConstants.MANUAL_TASK_SIGN_EMPTY);
            }
            if (Objects.isNull(taskCreatReq.getSendChannelType())) {
                throw exception(ErrorCodeConstants.MANUAL_TASK_CHANNEL_EMPTY);
            }
        }
        if (BusinessEnum.OperationType.SAVE_AND_SUBMIT.getType().equals(taskCreatReq.getAddDataType())){
            Assert.notBlank(taskCreatReq.getMessageFile(), ()-> new ServiceException(ErrorCodeConstants.TASK_CANNOT_SUBMIT));
            Assert.notBlank(taskCreatReq.getMessageTemplateCode(), ()-> new ServiceException(ErrorCodeConstants.TEMPLATE_CODE_NULL));
        }
        if (StrUtil.isNotBlank(taskCreatReq.getMessageTemplateCode())){
            boolean exists = templateRepository.existsTemplateCodeByType(taskCreatReq.getMessageTemplateCode(),
                    SmsTemplateTypeEnum.MANUAL.getCode());
            // Assert.isTrue(Boolean.TRUE.equals(exists), "选择的手动通知模板无效或不存在");
            Assert.isTrue(exists, ()-> new ServiceException(ErrorCodeConstants.TASK_SELECTED_ERROR));
        }
    }


    /**
     *  获取任务发送时间
     * @param notificationTask 通知任务
     * @return String
     * */
    public String getTaskSendScheduleTime(NotificationTask notificationTask) {
        if(Objects.isNull(notificationTask)) {
            return "";
        }
        if (TaskTypeEnum.AUTO_TASK.getType().equals(notificationTask.getTaskType())
                && TaskSendTypeEnum.REALTIME.getCode().equals(notificationTask.getTaskSendType())) {
            return REALTIME_SEND;
        }
        return notificationTask.getTaskSendScheduleTime();
    }

    /**
     *  放回任务发送时间类型
     *  @param sendType 发送时间类型
     *  @return String
     * */
    public String getTaskSendTimeType(Integer sendType) {
        TaskSendTypeEnum taskSendTypeEnum = TaskSendTypeEnum.getTaskEnumByCode(sendType);
        if (Objects.isNull(taskSendTypeEnum)) {
            return "";
        }
        return taskSendTypeEnum.getDesc();
    }

    /**
     *  返回任务的停用时间
     * @param taskStatus 任务状态
     * @param deactivateTime 停止时间
     * @return String
     * */
    public String getTaskDeactivateTime(Integer taskStatus, LocalDateTime deactivateTime) {
        if (Objects.isNull(taskStatus) || Objects.isNull(deactivateTime)) {
            log.info("发送任务状态或任务停用时间为空");
            return null;
        }
        if (TaskStatusEnum.START.getStatus().equals(taskStatus)) {
            log.info("当前发送任务为启用状态，无需停用时间");
            return null;
        }
        return TimeFormatUtil.localDateToString(deactivateTime);
    }

    /**
     *  返回任务状态描述
     * @param  status 任务状态吗
     * @return String
     * */
    public String getTaskStatus(Integer status) {
        TaskStatusEnum taskStatusEnum = TaskStatusEnum.getTaskStatusEnumByCode(status);
        if (Objects.isNull(taskStatusEnum)) {
            log.info("按照任务状态吗返回任务状态枚举为空, status:{}", status);
            return null;
        }
        return taskStatusEnum.getDesc();
    }


    /**
     *   按照模板code返回模板内容
     * @param templateCode  模板code
     * @return String
     * */
    public String getTemplateContentByCode(String templateCode) {
        MessageTemplate messageTemplate = templateRepository.getMessageTemplateByCode(templateCode);
        if (Objects.isNull(messageTemplate)) {
            log.info("按照模板code返回模板内容， 模板数据为空");
            return null;
        }
        return messageTemplate.getTemplateContent();
    }


    /**
     *  分页查询短信通知任务列表页
     * @param taskPageListReq 分页插叙通知任务列表页入参
     * @return Page<NotificationTask>
     * */
    public Page<NotificationTask> queryTaskPageList(TaskPageListReq taskPageListReq) {
        Page<NotificationTask> page = new Page<>(taskPageListReq.getPageNo(), taskPageListReq.getPageSize());
        LambdaQueryWrapper<NotificationTask> wrapper = new LambdaQueryWrapper<>();

        // 构建基础查询条件
        buildBasicQueryConditions(wrapper, taskPageListReq);

        // 构建状态和草稿版本查询条件
        buildStatusAndDraftConditions(wrapper, taskPageListReq);

        // 构建排序条件
        buildSortConditions(wrapper, taskPageListReq);

        // 添加业务代码和默认排序
        wrapper.eq(NotificationTask::getBusinessCode, taskPageListReq.getBusinessCode())
                .orderByDesc(NotificationTask::getUpdatedTime)
                .orderByDesc(NotificationTask::getId);

        return taskMapper.selectPage(page, wrapper);
    }

    /**
     * 构建基础查询条件
     */
    private void buildBasicQueryConditions(LambdaQueryWrapper<NotificationTask> wrapper, TaskPageListReq taskPageListReq) {
        wrapper.eq(StringUtils.isNotBlank(taskPageListReq.getTaskCode()),
                        NotificationTask::getTaskCode, taskPageListReq.getTaskCode())
                .eq(Objects.nonNull(taskPageListReq.getTaskType()),NotificationTask::getTaskType,taskPageListReq.getTaskType())
                .eq(NotificationTask::getIsDeleted, IsDeleteEnum.NO.getStatus());
        String taskName = processTaskName(taskPageListReq.getTaskName());
        if (StringUtils.isNotBlank(taskName)) {
            wrapper.like(NotificationTask::getTaskName, taskName);
        }
    }

    /**
     * 构建状态和草稿版本查询条件
     */
    public void buildStatusAndDraftConditions(LambdaQueryWrapper<NotificationTask> wrapper, TaskPageListReq taskPageListReq) {
        boolean hasStatusList = CollUtil.isNotEmpty(taskPageListReq.getStatusList());
        boolean isDraftVersion = TaskDraftVersionEnum.YES.getDraftVersion().equals(taskPageListReq.getDraftVersion());

        if (hasStatusList && isDraftVersion) {
            // 两个都不为空时使用OR条件
            wrapper.and(qw -> qw
                    .in(NotificationTask::getStatus, taskPageListReq.getStatusList())
                    .or()
                    .eq(NotificationTask::getDraftVersion, taskPageListReq.getDraftVersion())
            );
        } else {
            if (hasStatusList) {
                wrapper.in(NotificationTask::getStatus, taskPageListReq.getStatusList());
            }
            if (Objects.nonNull(taskPageListReq.getDraftVersion())) {
                wrapper.eq(NotificationTask::getDraftVersion, taskPageListReq.getDraftVersion());
            }
        }
    }

    /**
     * 构建排序条件
     */
    public void buildSortConditions(LambdaQueryWrapper<NotificationTask> wrapper, TaskPageListReq taskPageListReq) {
        if (Objects.nonNull(taskPageListReq.getStartTimeSortType())) { // 启用时间排序
            applySortCondition(wrapper, taskPageListReq.getStartTimeSortType(),
                    NotificationTask::getActivateTime);
        } else if (Objects.nonNull(taskPageListReq.getEndTimeSortType())) { // 停用时间排序
            applySortCondition(wrapper, taskPageListReq.getEndTimeSortType(),
                    NotificationTask::getDeactivateTime);
        } else if (Objects.nonNull(taskPageListReq.getStatusSortType())) { // 状态排序
            applySortCondition(wrapper, taskPageListReq.getStatusSortType(),
                    NotificationTask::getStatus);
        }
    }

    /**
     * 应用排序条件的通用方法
     */
    public <T> void applySortCondition(LambdaQueryWrapper<NotificationTask> wrapper,
                                       Integer sortType,
                                       SFunction<NotificationTask, T> column) {
        if (SortEnum.ASC.getSortType().equals(sortType)) {
            wrapper.orderByAsc(column);
        } else {
            wrapper.orderByDesc(column);
        }
    }

    /**
     * 处理任务名称，确保特定字符被转义
     *
     * @param taskName 原始任务名称，可能包含需要转义的特殊字符
     * @return 转义后的任务名称如果输入的任务名称为空或只包含空白字符，则返回原始任务名称
     */
    public String processTaskName(String taskName) {
        if (StringUtils.isNotBlank(taskName)) {
            // 正确地替换字符串并返回新字符串
            return taskName.replace("%", "\\%").replace("_", "\\_");
        }
        return taskName;
    }
}
