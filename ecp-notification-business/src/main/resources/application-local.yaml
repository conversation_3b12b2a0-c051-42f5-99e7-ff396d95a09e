--- #################### 数据库相关配置 ####################
spring:
  # 数据源配置项
  autoconfigure:
    exclude:
      - com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure # 排除 Druid 的自动配置，使用 dynamic-datasource-spring-boot-starter 配置多数据源
  datasource:
    druid: # Druid 【监控】相关的全局配置
      web-stat-filter:
        enabled: true
      stat-view-servlet:
        enabled: true
        allow: # 设置白名单，不填则允许所有访问
        url-pattern: /druid/*
        login-username: # 控制台管理用户名和密码
        login-password:
      filter:
        stat:
          enabled: true
          log-slow-sql: true # 慢 SQL 记录
          slow-sql-millis: 100
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
    dynamic: # 多数据源配置
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 5 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 FROM DUAL # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
      datasource:
        ecp_system:
          name: ecp_system
          url: *************************************************************************************************/${spring.datasource.dynamic.datasource.ecp_system.name}?allowMultiQueries=true&useUnicode=true&useSSL=false&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&autoReconnect=true&nullCatalogMeansCurrent=true # MySQL Connector/J 8.X 连接的示例
          username: admin
          password: 'cxK4R4syohatKgrn0PYz'
        master:
          name: ecp_notification
          url: **********************************************************************************************/${spring.datasource.dynamic.datasource.master.name}?allowMultiQueries=true&useUnicode=true&useSSL=false&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&autoReconnect=true&nullCatalogMeansCurrent=true # MySQL Connector/J 8.X 连接的示例
          driver-class-name: com.mysql.jdbc.Driver
          username: admin
          password: 'cxK4R4syohatKgrn0PYz'
        slave: # 模拟从库，可根据自己需要修改 # 模拟从库，可根据自己需要修改
          name: ecp_notification
          url: *************************************************************************************************/${spring.datasource.dynamic.datasource.slave.name}?allowMultiQueries=true&useUnicode=true&useSSL=false&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&autoReconnect=true&nullCatalogMeansCurrent=true # MySQL Connector/J 8.X 连接的示例
          driver-class-name: com.mysql.jdbc.Driver
          username: admin
          password: 'cxK4R4syohatKgrn0PYz'

  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  redis:
    redisson:
       config: |
        masterSlaveServersConfig:
          idleConnectionTimeout: 10000
          connectTimeout: 10000
          timeout: 3000
          retryAttempts: 3
          retryInterval: 1500
          failedSlaveReconnectionInterval: 3000
          failedSlaveCheckInterval: 60000
          password: ${REDIS_PASSWD:uiMYufcA8urorYSK}
          subscriptionsPerConnection: 5
          clientName: null
          loadBalancer: !<org.redisson.connection.balancer.RoundRobinLoadBalancer> {}
          subscriptionConnectionMinimumIdleSize: 1
          subscriptionConnectionPoolSize: 50
          slaveConnectionMinimumIdleSize: 6
          slaveConnectionPoolSize: 24
          masterConnectionMinimumIdleSize: 6
          masterConnectionPoolSize: 12
          readMode: "MASTER"
          subscriptionMode: "MASTER"
          slaveAddresses:
            - "rediss://replica.redis-dev-ecp-1.hych0h.cnw1.cache.amazonaws.com.cn:6379"
          masterAddress: "rediss://master.redis-dev-ecp-1.hych0h.cnw1.cache.amazonaws.com.cn:6379"
          database: 0
        threads: 16
        nettyThreads: 32
        codec: !<org.redisson.codec.Kryo5Codec> {}
        transportMode: "NIO"

#      file: classpath:redisson.yaml
#    host: ************* # 地址
#    port: 6379 # 端口
#    database: 1 # 数据库索引
#    password: 123456 # 密码，建议生产环境开启

  kafka:
    bootstrap-servers: b-1.devjlrecpmsk.4x4522.c2.kafka.cn-northwest-1.amazonaws.com.cn:9092,b-2.devjlrecpmsk.4x4522.c2.kafka.cn-northwest-1.amazonaws.com.cn:9092
    producer:
      # 发生错误后，消息重发的次数。
      retries: 3
      #当有多个消息需要被发送到同一个分区时，生产者会把它们放在同一个批次里。该参数指定了一个批次可以使用的内存大小，按照字节数计算。
#      batch-size: 16384
      # 设置生产者内存缓冲区的大小。
#      buffer-memory: 33554432
      # 键的序列化方式
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      # 值的序列化方式
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      # acks=0 ： 生产者在成功写入消息之前不会等待任何来自服务器的响应。
      # acks=1 ： 只要集群的首领节点收到消息，生产者就会收到一个来自服务器成功响应。
      # acks=all ：只有当所有参与复制的节点全部收到消息时，生产者才会收到一个来自服务器的成功响应。
      acks: 1
    consumer:
      group-id: ${spring.application.name}
      auto-commit-interval: 100
      enable-auto-commit: true
#      auto-offset-reset: earliest
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      # 在侦听器容器中运行的线程数。
#      concurrency: 5
      # listner负责ack，每调用一次，就立即commit
#      ack-mode: manual_immediate
      missing-topics-fatal: false


--- #################### MQ 消息队列相关配置 ####################
#spring:
#  cloud:
#    stream:
#      rocketmq:
#        # RocketMQ Binder 配置项，对应 RocketMQBinderConfigurationProperties 类
#        binder:
#          name-server: **************:9876 # RocketMQ Namesrv 地址

--- #################### 定时任务相关配置 ####################
xxl:
  job:
    admin:
      addresses: http://127.0.0.1:9090/xxl-job-admin # 调度中心部署跟地址
    enabled: false

--- #################### 服务保障相关配置 ####################

# Lock4j 配置项
lock4j:
  acquire-timeout: 3000 # 获取分布式锁超时时间，默认为 3000 毫秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 毫秒

--- #################### 监控相关配置 ####################

# Actuator 监控端点的配置项
management:
  endpoints:
    web:
      base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
      exposure:
        include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。

# Spring Boot Admin 配置项
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        instance:
          service-host-type: IP # 注册实例时，优先使用 IP [IP, HOST_NAME, CANONICAL_HOST_NAME]
      # Spring Boot Admin Server 服务端的相关配置
      context-path: /admin # 配置 Spring

--- #################### 微信公众号相关配置 ####################
wx: # 参见 https://github.com/Wechat-Group/WxJava/blob/develop/spring-boot-starters/wx-java-mp-spring-boot-starter/README.md 文档
  mp:
    # 公众号配置(必填)
    app-id: wx041349c6f39b268b
    secret: 5abee519483bc9f8cb37ce280e814bd0
    # 存储配置，解决 AccessToken 的跨节点的共享
    config-storage:
      type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
      key-prefix: wx # Redis Key 的前缀 TODO ：解决下 Redis key 管理的配置
      http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台

--- #################### RR低代码相关配置 ####################

# RR低代码配置项，设置当前项目所有自定义的配置
lc:
  xss:
    enable: false
    exclude-urls: # 如下两个 url，仅仅是为了演示，去掉配置也没关系
      - ${spring.boot.admin.context-path}/** # 不处理 Spring Boot Admin 的请求
      - ${management.endpoints.web.base-path}/** # 不处理 Actuator 的请求
  pay:
    pay-notify-url: http://niubi.natapp1.cc/api/pay/order/notify
    pay-return-url: http://niubi.natapp1.cc/api/pay/order/return
    refund-notify-url: http://niubi.natapp1.cc/api/pay/refund/notify
  demo: false # 开启演示模式

justauth:
  enabled: true
  type:
    DINGTALK: # 钉钉
      client-id: dingvrnreaje3yqvzhxg
      client-secret: i8E6iZyDvZj51JIb0tYsYfVQYOks9Cq1lgryEjFRqC79P3iJcrxEwT6Qk2QvLrLI
      ignore-check-redirect-uri: true
    WECHAT_ENTERPRISE: # 企业微信
      client-id: wwd411c69a39ad2e54
      client-secret: 1wTb7hYxnpT2TUbIeHGXGo7T0odav1ic10mLdyyATOw
      agent-id: 1000004
      ignore-check-redirect-uri: true
  cache:
    type: REDIS
    prefix: 'social_auth_state:' # 缓存前缀，目前只对 Redis 缓存生效，默认 JUSTAUTH::STATE::
    timeout: 24h # 超时时长，目前只对 Redis 缓存生效，默认 3 分钟
# 适应用 Mybatis log
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

dataCenterFlag: product-data-center
workFlag: product-work

access:
  token:
    url: https://identity.dev-iam.jlrinfo.cn/gateway/oauth2/realms/root/realms/customer/access_token
    grantType: client_credentials
    scope: urn:iam2-mgd-v1:scopes:a2a-vcs:cnapim-default
    clientId: a2a-vcs
    clientAssertionType: urn:ietf:params:oauth:client-assertion-type:jwt-bearer

forgeRock:
  requestUrl: https://identity.dev-iam.jlrinfo.cn:443/gateway/oauth2/realms/root/realms/customer/access_token
  clientId: a2a-vcs
  jti: 1jBxCpWVeIal0EFmeJfUU
  privateKey: -----BEGIN PRIVATE KEY----- MIIJQwIBADANBgkqhkiG9w0BAQEFAASCCS0wggkpAgEAAoICAQCyGaFkwKEYQm2v Zq+fGb+OY4VU1Tl49gnWD9vEocnhnTVcq7eBL5phW1sBS5XZ/y7X4teNWiNT8azN B+WU7n70W7ppdprN0htSCtB/Ds+bunDKCL5fSgSsNSIgMhTSKPsi8/uYZYRwOvmJ e9f28ck7u0iBSj/vyhRKPrmlkG8BEC4ug02Ux9hJCoxQ6E6ScNaYGDBb7hgVfHMc Zxh50HRc4pjTu3KHn8x9RNdsuCMgQlMLdsIRRTv0jLTaoK8Cbn8T7+8M8dgi6XU+ ReQX8iSLk+Dxhpa+LnOItRsuF3RV5l/vV6d3I0q1mJP2PK0pSSttGnCqgk6Lvew1 Zi+Q/EFEtgo+7M49e1z/4Jo/S0Rvpi/ERtVdM9MkzgnPdEoPZrdXBJkE9Ovo+zwx zJapR4UziJOJJ1Cl/nNTaYrRpjIX5Qanzm2MdPYyaq2SugKDT2lmpRc1bWvSihOr CV65lvdr7VQx/8CmrbVt/w137mMFsQMATO8Id8jNBAzbqRaCa3MMj/a7jnqg1pr2 2BSaHaWaoByDKFMauNgMYWTWC51+v4hyCCmMR2X73+Ti7HLkN2ode+gy7y//ctMi FPLSxar555FWItNxrOkiPXSGF/01zATDBNF2RM35wl5AHbM2KWFiOaPSSKfQkd0G u2yBPHs0P9on5FjcKmsW23WC+lKu9wIDAQABAoICAQCdCVBvgBILS/de41ITHSb0 K/hSdrf4WikOIGmNFYLmdjGpBpgFRq3/DghOOhFlaEYrgQ7CyinASKYvGrlp+mji mUbi2vtgY9RHINX6UlroWxo8aj1B1AgOt8LhVIdbT1sw1KSRGWIPe1Cv0wz35vWi v14M8iUyD6KYXHpg69uUummeBZSQjS2+KZjTdiJhqTDuZtWtGWEPAO6WqqjAL/eK Nxq+BtfTkilCg0LXz/zY4uJKh2pSxBM7bB3DcE8f+8H9GGBVr6D+F4+TWxjlrFJg 6AE9tYiRJHAmqFnQ6ElhvIH2jflRaULkoCZGxfWBI80wpFGXZtJPESWHaBSk6Ge6 71t+DpAWko72lauz6z1r1Lj/sAuPFhczZpsBoc6EDKBUUJ37cgJlv6JZh6UMB9R5 uNXSMY2mhsEq7NbgkboUxj2m+Yzw2XxoVYdlzcrpLvr2KB9y4hUxZmGzSA1jzleB pn01ucCYqdqrl/arKyX7E/ldzYMB95T0Q81fhEpYCVN0WHS1ip1G6/TSk0vgttCW OHjWhoq3UmSRIoU4LT6AEnxgVTcA3j+8Qbcwqz8q66Q673lfeca+Rcs4onOGeMCc zvKmSig4kfwkHtdYAx0iZjMmrwehn2gFGLQBR5cwzgjISn7E7tOIq7CodTiK0Alx /KVKuPASROCi53GjPcaSwQKCAQEA3eooMw2HMjbQFzG466llsTVk4LmwDxUm9OAr FSFPvOZKBBe8cQCQ6XCMfiRZrAY8G6z7UsYwIKkwgglOoPrzfqMQ/fxA2FG9Q4rU H8uxi5CiyLbslz2ds3bpeUMWM3ZH+HkDbFjBdaQUkp1vUvdidW+DoiCtou2hi9Kw liQUhdamcOKXab7oNcQOGuq4H0GePE43bt68oDBWWu0lZyYAjqLTlpuW34/wIyqC SF2qxLaX/Ab2/agzfpsDX5eQQ4x3PYuUu4i20VtLMU3nvAvxw4OpiT/iXptzawGq tlP3m8jo2uwWrjoe0CncKjT3goLFFmyOJGUslaEm2xdRuQ+Q0QKCAQEAzXSnYxk/ bl1Inop/PJxGS3fnOyyu3S0p65BL083XxqVx+DFYdr6pp6GW9i/OoiFMMdlP7R61 0WwIYq6vIg1/JT8SDiwoJrKbuNFNx3pmuXiAglJuc1pFVop/Hux+SrmtjDiqcC9P 0um1++28/Eai19M8173nUUJbdmCEwBhb0stA5ld2KoPyo9FTJnvU2dGFWzBf6TAw Nn89SAy8COLbH0EP7YWIOUXOoVxwa06VyUmje+i3hYOLJcSkXf5R/Gdn5jHtZmmY jLrZiq2Q0ZEAQUqzz2l9rpC1ETE2bql3E4VBNThngNaDgCVJNypfGNqgzjk/fTi8 zi6C8J7ucTJ1RwKCAQEAjbtc22Z7f520f+vY4GK7uVeRy1mtUaS/0cxOl9tDszEd +skyKLYk14Nzp6kNwKnmwAJWLgK4gFOu0I6jMnu1Ap+hXaY7wC1MhRPo8TnGdo30 +d6i8uF+lD2RVkp7pniX9OFNR2jUfo9COHXmwQWRU9HaL7VRUOsWr1fsQqhelzCF 8zk1XWt5ocVLAPQCjqv5JSkLjHsj2+ejb8VgiyWhW3gxTVKoJLcotz0SIqf4RlUc cvH70/wWj3jvyaDimdHf4mYAo1no8YOuXZoVNGwoiCfSIv+zU5tTNvfEkVjFnzDN T+1TewlR3r+thyN3JVriB7Vjf2lC9XoMeAfHwTto0QKCAQAVt0HGALIqqSN21m6y alYnvemr4IIvEIoAwwSww24OKgiM0jJmgqqN9E9vgAgYLOrPzgdbqcZP91o/i/Uo 5UJOHod7BgHMbU52T3EoJ3DaI7l5jNrtyFnR1tuu2hJTNaL5Ujf5mZaXVWqN0UgL Zi/qHgKtTv43IYtnTLZbU3fJJQCcGBx+S+oGlKZ0R9xslW/B/q/0hsphyDsi9W2e htPgTtEvKtEGNLGtbobXYLwGRYlJHIThUNERUeKJqMrjoc5ij15nV7vvDATYWpYX AHuFay7sOls1a0goJAp70d+rKYeF54tRG9tUXRyK/yiECP+8LGLginGyXUorwrCz OEEbAoIBAH672dRMJu4MrRin0vkhbzyM+jY/d44tuIC3OzL5JQiys8aeU1lj/rOs 2CygbD9m4FusMtQlrBtcsIiYC2mNGtaz7mwnWvognl03X+pkNAIzFqMyHYLdbc+a quiRswc61kvIA+/Tfp+2WbkseYdc8HjpdKTlh5GUh0D1V7fBmN9MJpTX5eWRE7OF 4Z8BcrpKm96CQ1yXHIcMV49qYytR6//p4Sn6wQDV89eBW1DTyOz/E9cLmiNssB0s vKpdO+X/WQC727hGOa4cZsbyYgi2kn/EzEe8QCdmUYlo6BNrfgyF2CwyM2zGGFrR BokbR3nr0wfaB+aRSyYf5wgSaub53EA= -----END PRIVATE KEY-----
  publicKey: -----BEGIN PUBLIC KEY----- MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAshmhZMChGEJtr2avnxm/ jmOFVNU5ePYJ1g/bxKHJ4Z01XKu3gS+aYVtbAUuV2f8u1+LXjVojU/GszQfllO5+ 9Fu6aXaazdIbUgrQfw7Pm7pwygi+X0oErDUiIDIU0ij7IvP7mGWEcDr5iXvX9vHJ O7tIgUo/78oUSj65pZBvARAuLoNNlMfYSQqMUOhOknDWmBgwW+4YFXxzHGcYedB0 XOKY07tyh5/MfUTXbLgjIEJTC3bCEUU79Iy02qCvAm5/E+/vDPHYIul1PkXkF/Ik i5Pg8YaWvi5ziLUbLhd0VeZf71endyNKtZiT9jytKUkrbRpwqoJOi73sNWYvkPxB RLYKPuzOPXtc/+CaP0tEb6YvxEbVXTPTJM4Jz3RKD2a3VwSZBPTr6Ps8McyWqUeF M4iTiSdQpf5zU2mK0aYyF+UGp85tjHT2MmqtkroCg09pZqUXNW1r0ooTqwleuZb3 a+1UMf/Apq21bf8Nd+5jBbEDAEzvCHfIzQQM26kWgmtzDI/2u456oNaa9tgUmh2l mqAcgyhTGrjYDGFk1gudfr+IcggpjEdl+9/k4uxy5DdqHXvoMu8v/3LTIhTy0sWq +eeRViLTcazpIj10hhf9NcwEwwTRdkTN+cJeQB2zNilhYjmj0kin0JHdBrtsgTx7 ND/aJ+RY3CprFtt1gvpSrvcCAwEAAQ== -----END PUBLIC KEY-----

send:
  smsUrl: ${APIM_SMSURL:https://apimn-uat.awscn.jlrint.com/business/sms/send/v1.0.0}
  smsBatchUrl: ${APIM_SMS_BATCH_URL:https://apimn-uat.awscn.jlrint.com/business/sms/batch_send/v1.0.0}
  updateUrl: ${APIM_UPDATEURL:https://apimn-uat.jaguarlandrover.cn/business/sms/status_report/v1.0.0}
  apiKey: ${APIM_APIKEY:NZyNtPIWWCanL2oKTFGizjnIZaJSNTK8MhQLQUD7}
  apiSecret: ${APIM_APISECRET:Iunxd1xDDepOTT3VaMHRxeBEZZYNenPtUQod-5Fg}
  path: /business/sms/send/v1.0.0
  batchPath: /business/sms/batch_send/v1.0.0
  updatePath: /business/sms/status_report/v1.0.0

jaguarlandrover:
  appKey: evoque
  secret: Ec3199a1f
  shortLinkUrl: https://dpit-dev.jaguarlandrover.cn/wxma/wx/urlLink

sms:
  hostPath:
    batch: https://preprod.cil.jlr-apps.cn
  notification:
    account: ${SMS_NOTIFICATION_ACCOUNT:h52016}
    password: ${SMS_NOTIFICATION_PASSWORD:c9a6df47e35556003f29fd0d603a9b4a}
  market:
    account: ${SMS_MARKET_ACCOUNT:h51018}
    password: ${SMS_MARKET_PASSWORD:03c80fced2cdf91f4fdfee2a18dc6720}
  bg-lre:
    notify:
      account: ${BG_SMS_NOTIFY_ACCOUNT}
      password: ${BG_SMS_NOTIFY_PASSWORD}
  response:
    updateFlag: false


excel:
  templateUrl:
    vin: ${SMS_EXCEL_VIN_URL:https://ecp-static-dev.jaguarlandrover.cn/file/VINAndPhoneNumberUploadTemplate.xlsx}
    phone: ${SMS_EXCEL_PHONE_URL:https://ecp-static-dev.jaguarlandrover.cn/file/PhoneNumberUploadTemplate.xlsx}

--- #################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: true # 1. 是否开启 Swagger 接文档的元数据
    path: /v3/api-docs
  swagger-ui:
    enabled: true # 2.1 是否开启 Swagger 文档的官方 UI 界面
    path: /swagger-ui.html

knife4j:
  enable: true # 2.2 是否开启 Swagger 文档的 Knife4j UI 界面
  setting:
    language: zh_cn


mp:
  msg:
    app: jet

sns:
  privateEndpoint: https://mqcsnsnw-uat.awscn.jlrint.com
  topicArn: arn:aws-cn:sns:cn-northwest-1:************:sns-ecp-vcs-template-message-dev-fifo-1040c5a9.fifo
  assumeRoleArn: arn:aws-cn:iam::************:role/R-DEV-ECP-SEND-SNS-ROLE
  assumeRoleArn2: arn:aws-cn:iam::************:role/sns-ecp-vcs-template-message-dev-1040c5a9.fifo
