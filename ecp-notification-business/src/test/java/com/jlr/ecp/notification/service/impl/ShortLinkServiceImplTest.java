package com.jlr.ecp.notification.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.jaguar.api.wechat.ShortLinkByWechatApi;
import com.jlr.ecp.jaguar.api.wechat.dto.GenUrlLinkReqDto;
import com.jlr.ecp.notification.enums.ErrorCodeConstants;
import com.jlr.ecp.notification.util.sign.AbstractSignature;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class ShortLinkServiceImplTest {

    @Mock
    private RestTemplate mockRestTemplate;
    @Mock
    private ShortLinkByWechatApi mockShorLinkByWechatApi;

    @InjectMocks
    @Spy
    private ShortLinkServiceImpl shortLinkServiceImplUnderTest;

    @Before
    public void setUp() {
        shortLinkServiceImplUnderTest = new ShortLinkServiceImpl();
        shortLinkServiceImplUnderTest.jlrShortLinkServiceUrl = "jlrShortLinkServiceUrl";
        shortLinkServiceImplUnderTest.appKey = "appKey";
        shortLinkServiceImplUnderTest.secret = "secret";
        shortLinkServiceImplUnderTest.restTemplate = mockRestTemplate;
        shortLinkServiceImplUnderTest.shorLinkByWechatApi = mockShorLinkByWechatApi;
    }

    @Test
    public void testGenShortLink() throws Exception {
        // Setup
        final CommonResult<String> expectedResult = CommonResult.success("expectedShortLink"); // 假设一个成功的预期结果
        JSONObject responseBody = new JSONObject(); // 假设一个响应体内容
        responseBody.put("code", AbstractSignature.HTTP_SUCCESS_CODE);
        responseBody.put("result", "expectedShortLink");

        // 使用 eq 匹配器来匹配期望的第二个参数（JSONObject.class）
        when(mockRestTemplate.exchange(any(RequestEntity.class), eq(JSONObject.class))).thenReturn(new ResponseEntity<>(responseBody, HttpStatus.OK));

        // Run the test
        CommonResult<String> result = shortLinkServiceImplUnderTest.genShortLink("path");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGenShortLinkError() throws Exception {
        // Setup
        final CommonResult<String> expectedResult = CommonResult.error(ErrorCodeConstants.SMS_SHORT_LINK_FETCH_FAIL); // 假设一个成功的预期结果
        JSONObject responseBody = new JSONObject(); // 假设一个响应体内容
        responseBody.put("code", 500);
        responseBody.put("result", "expectedShortLink");

        // 使用 eq 匹配器来匹配期望的第二个参数（JSONObject.class）
        when(mockRestTemplate.exchange(any(RequestEntity.class), eq(JSONObject.class))).thenReturn(new ResponseEntity<>(responseBody, HttpStatus.OK));

        // Run the test
        CommonResult<String> result = shortLinkServiceImplUnderTest.genShortLink("path");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGenShortLinkResultEmpty() throws Exception {
        // Setup
        final CommonResult<String> expectedResult = null;

        final ResponseEntity<JSONObject> jsonObjectResponseEntity = new ResponseEntity<>(new JSONObject(0, false),
                HttpStatus.OK);
        when(mockRestTemplate.exchange(any(RequestEntity.class), eq(JSONObject.class))).thenReturn(jsonObjectResponseEntity);

        CommonResult<String> result = null;

        try {
            // Run the test
            result = shortLinkServiceImplUnderTest.genShortLink("path");
        } catch (Exception e) {
            log.error("testGenShortLink Exception:", e);
        }

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGenShortLinkArgEmpty() throws Exception {
        // Setup
        final CommonResult<String> expectedResult = CommonResult.success("");


        CommonResult<String> result = shortLinkServiceImplUnderTest.genShortLink("");


        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGenJaguarLink() {
        // Setup
        final CommonResult<String> expectedResult = CommonResult.error(304001, "生成短链失败");

        final ResponseEntity<JSONObject> jsonObjectResponseEntity = new ResponseEntity<>(new JSONObject(0, false),
                HttpStatus.INTERNAL_SERVER_ERROR);
        when(mockRestTemplate.exchange(any(RequestEntity.class), eq(JSONObject.class))).thenReturn(jsonObjectResponseEntity);
        // Run the test
        final CommonResult<String> result = shortLinkServiceImplUnderTest.genJaguarLink("path", "query");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

}
